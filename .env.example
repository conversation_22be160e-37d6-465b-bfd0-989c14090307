# Environment Variables for bolt2api

# Deployment Environment
NODE_ENV=production

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# API Configuration
BOLT_BASE_URL=https://bolt.new
API_TIMEOUT=30000
MAX_RETRIES=3

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Optional: External Services
# SENTRY_DSN=your_sentry_dsn_here
# DATADOG_API_KEY=your_datadog_key_here

# Optional: Database (if you want to store sessions/logs)
# DATABASE_URL=postgresql://user:password@host:port/database

# Optional: Redis (for rate limiting and caching)
# REDIS_URL=redis://localhost:6379

# Security
# JWT_SECRET=your_jwt_secret_here (if implementing JWT auth)
# ENCRYPTION_KEY=your_encryption_key_here (for encrypting stored cookies)

# Monitoring
# HEALTH_CHECK_INTERVAL=30000
# METRICS_ENABLED=true
