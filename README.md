# Bolt2API - API Wrapper for bolt.new Chat

A serverless API wrapper that intercepts bolt.new's chat functionality using session cookies for authentication.

## Features

- 🚀 **Cloud Run Deployment** - Deployed on Google Cloud Run
- 🔐 **<PERSON>ie Authentication** - Uses bolt.new session cookies
- 💬 **Chat API Proxy** - Intercepts bolt.new chat mode
- 🔄 **Streaming Support** - Handles bolt.new's streaming responses
- � **Request Logging** - Complete request/response monitoring

## Quick Start

### 1. Installation

```bash
npm install
```

### 2. Environment Setup

Copy the example environment file and configure:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000
BOLT_BASE_URL=https://bolt.new
```

### 3. Development

```bash
npm run dev
```

### 4. Deployment

#### Vercel (Recommended)

```bash
npm install -g vercel
vercel
```

#### Netlify

```bash
npm install -g netlify-cli
netlify deploy
```

## API Endpoints

### POST `/api/chat`

Send a chat message to bolt.new.

**Headers:**
- `Content-Type: application/json`
- `X-Session-Cookies: your_bolt_session_cookies`

**Request Body:**
```json
{
  "message": "Create a React todo app",
  "mode": "build",
  "projectId": "optional_project_id",
  "context": "optional_context",
  "stream": false,
  "timeout": 30000
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "I'll help you create a React todo app...",
    "projectId": "sb1-abc123",
    "files": [...],
    "actions": [...]
  },
  "requestId": "uuid",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "processingTime": 1500
}
```

### POST `/api/auth`

Validate bolt.new session cookies.

**Request Body:**
```json
{
  "cookies": "your_bolt_session_cookies"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "organizationId": "22851",
    "userId": "user123",
    "tokenUsage": {
      "used": 1000000,
      "remaining": 9000000,
      "limit": 10000000
    }
  }
}
```

### GET `/api/health`

Check API health status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "1.0.0",
    "uptime": 3600,
    "services": {
      "boltApi": "up"
    }
  }
}
```

### GET/POST `/api/projects`

Manage projects and chat history.

**GET** - Retrieve chat history:
```
GET /api/projects?organizationId=22851&limit=50
```

**POST** - Create new project:
```json
{
  "template": "react-vite",
  "name": "My New Project",
  "sessionCookies": "your_cookies"
}
```

## Authentication

This API uses bolt.new session cookies for authentication. You need to:

1. **Login to bolt.new** using Google OAuth
2. **Extract session cookies** from your browser
3. **Use cookies** in API requests

### Extracting Cookies

#### Method 1: Browser DevTools
1. Open bolt.new in your browser
2. Login with Google
3. Open DevTools (F12) → Application → Cookies
4. Copy the cookie values for:
   - `_stackblitz_session`
   - `sb_session`
   - `sb_user_id`
   - `sb_org_id` (if using team account)

#### Method 2: Browser Extension
Use a cookie export extension to get the full cookie string.

#### Method 3: cURL
```bash
# After logging in, check your cookies
curl -I https://bolt.new/api/token \
  -H "Cookie: your_extracted_cookies"
```

### Using Cookies in Requests

#### Option 1: Header
```bash
curl -X POST https://your-api.vercel.app/api/chat \
  -H "Content-Type: application/json" \
  -H "X-Session-Cookies: _stackblitz_session=abc123; sb_session=def456" \
  -d '{"message": "Hello"}'
```

#### Option 2: Request Body
```bash
curl -X POST https://your-api.vercel.app/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello",
    "sessionCookies": "_stackblitz_session=abc123; sb_session=def456"
  }'
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment | `production` |
| `ALLOWED_ORIGINS` | CORS origins | `http://localhost:3000` |
| `BOLT_BASE_URL` | bolt.new URL | `https://bolt.new` |
| `API_TIMEOUT` | Request timeout | `30000` |
| `MAX_RETRIES` | Max retry attempts | `3` |
| `RATE_LIMIT_REQUESTS` | Rate limit | `100` |
| `RATE_LIMIT_WINDOW` | Rate limit window | `60000` |

### CORS Configuration

Configure allowed origins in your environment:

```env
ALLOWED_ORIGINS=https://yourdomain.com,https://anotherdomain.com
```

## Error Handling

The API returns standardized error responses:

```json
{
  "success": false,
  "error": {
    "code": "authentication_error",
    "message": "Session has expired",
    "requestId": "uuid",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Error Codes

- `validation_error` - Invalid request data
- `authentication_error` - Invalid or expired session
- `api_error` - bolt.new API error
- `timeout_error` - Request timeout
- `rate_limit_exceeded` - Too many requests
- `internal_error` - Server error

## Development

### Project Structure

```
bolt2api/
├── api/                 # Serverless functions
│   ├── chat.ts         # Chat endpoint
│   ├── auth.ts         # Authentication
│   ├── health.ts       # Health check
│   └── projects.ts     # Projects management
├── src/
│   ├── types/          # TypeScript types
│   ├── utils/          # Utilities
│   └── services/       # Business logic
├── package.json
├── tsconfig.json
├── vercel.json         # Vercel config
└── README.md
```

### Scripts

```bash
npm run dev          # Start development server
npm run build        # Build TypeScript
npm run test         # Run tests
npm run lint         # Lint code
npm run type-check   # Type checking
```

## Testing

Test the API endpoints:

```bash
# Health check
curl https://your-api.vercel.app/api/health

# Authentication test
curl -X POST https://your-api.vercel.app/api/auth \
  -H "Content-Type: application/json" \
  -d '{"cookies": "your_cookies_here"}'

# Chat test
curl -X POST https://your-api.vercel.app/api/chat \
  -H "Content-Type: application/json" \
  -H "X-Session-Cookies: your_cookies_here" \
  -d '{"message": "Hello, create a simple React app"}'
```

## Deployment

### Vercel

1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel`
4. Set environment variables in Vercel dashboard

### Netlify

1. Install Netlify CLI: `npm i -g netlify-cli`
2. Login: `netlify login`
3. Deploy: `netlify deploy`
4. Set environment variables in Netlify dashboard

### AWS Lambda

Use the Serverless Framework or AWS SAM for Lambda deployment.

## Security Considerations

- **Cookie Security**: Session cookies are sensitive - handle with care
- **CORS**: Configure appropriate origins for your use case
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **Logging**: Sensitive data is automatically redacted from logs
- **HTTPS**: Always use HTTPS in production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the network analysis documentation
- Review the API documentation

---

Built with ❤️ for the bolt.new community
