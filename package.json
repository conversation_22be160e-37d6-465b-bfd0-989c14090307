{"name": "bolt2api", "version": "1.0.0", "description": "Serverless API wrapper for bolt.new chat functionality", "main": "dist/index.js", "scripts": {"dev": "vercel dev", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@vercel/node": "^3.0.21", "cookie": "^0.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "node-fetch": "^2.7.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/cookie": "^0.6.0", "@types/cors": "^2.8.17", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "vercel": "^32.5.3"}, "keywords": ["bolt.new", "api", "serverless", "chat", "ai"], "author": "Your Name", "license": "MIT"}