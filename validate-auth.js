const fs = require('fs');

// Validate and test authentication data
async function validateAuth() {
  console.log('🔍 Validating Authentication Data...\n');
  
  // 1. Check cookie format
  console.log('📋 1. Cookie Format Validation:');
  const cookies = fs.readFileSync('./sample-cookies.txt', 'utf8').trim();
  
  console.log('Cookie length:', cookies.length);
  console.log('Cookie format check:');
  
  // Check for required cookies
  const requiredCookies = ['_session', 'activeOrganizationId'];
  const cookieObj = {};
  
  cookies.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookieObj[name] = value;
    }
  });
  
  requiredCookies.forEach(required => {
    const present = cookieObj[required] ? '✅' : '❌';
    console.log(`  ${present} ${required}: ${cookieObj[required] ? 'Present' : 'Missing'}`);
  });
  
  // Check format issues
  const formatChecks = [
    { name: 'No trailing semicolon', check: !cookies.endsWith(';') },
    { name: 'Uses semicolon+space separator', check: cookies.includes('; ') },
    { name: 'No domain attributes', check: !cookies.includes('Domain=') },
    { name: 'No path attributes', check: !cookies.includes('Path=') },
    { name: 'No security attributes', check: !cookies.includes('Secure') && !cookies.includes('HttpOnly') }
  ];
  
  formatChecks.forEach(check => {
    const status = check.check ? '✅' : '❌';
    console.log(`  ${status} ${check.name}`);
  });
  
  // 2. Test direct bolt.new authentication
  console.log('\n🌐 2. Testing Direct bolt.new Authentication:');
  
  try {
    const testPayload = {
      id: "auth-test-" + Date.now(),
      errorReasoning: null,
      featurePreviews: { diffs: false, reasoning: false },
      framework: "DEFAULT_TO_DEV",
      isFirstPrompt: false,
      messages: [{ id: "test-msg", role: "user", content: "Test authentication" }],
      metrics: { importFilesLength: 0, fileChangesLength: 0 },
      projectId: "49956303",
      promptMode: "discussion",
      stripeStatus: "not-configured",
      supportIntegrations: true,
      usesInspectedElement: false
    };
    
    console.log('Making direct request to bolt.new...');
    
    const directResponse = await fetch('https://bolt.new/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://bolt.new',
        'Referer': 'https://bolt.new/'
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log('Direct response status:', directResponse.status);
    
    if (directResponse.status === 200) {
      console.log('✅ Direct authentication SUCCESSFUL');
      const data = await directResponse.json();
      console.log('Response preview:', JSON.stringify(data).substring(0, 200) + '...');
    } else {
      console.log('❌ Direct authentication FAILED');
      const errorText = await directResponse.text();
      console.log('Error response:', errorText);
      
      if (directResponse.status === 401) {
        console.log('\n💡 401 Unauthorized suggests:');
        console.log('   - Cookies may be expired');
        console.log('   - Missing required authentication header');
        console.log('   - Session tied to different IP/browser');
      }
    }
    
  } catch (error) {
    console.error('❌ Direct test failed:', error.message);
  }
  
  // 3. Test proxy authentication
  console.log('\n🔄 3. Testing Proxy Authentication:');
  
  try {
    const proxyResponse = await fetch('https://bolt2api-corrected-rf6frxmcca-ew.a.run.app/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      },
      body: JSON.stringify({
        id: "proxy-test-" + Date.now(),
        messages: [{ id: "test-msg", role: "user", content: "Test proxy" }],
        projectId: "49956303",
        promptMode: "discussion"
      })
    });
    
    console.log('Proxy response status:', proxyResponse.status);
    
    if (proxyResponse.status === 200) {
      console.log('✅ Proxy authentication SUCCESSFUL');
    } else {
      console.log('❌ Proxy authentication FAILED');
      const errorText = await proxyResponse.text();
      console.log('Proxy error:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Proxy test failed:', error.message);
  }
  
  // 4. Recommendations
  console.log('\n📝 4. Recommendations:');
  
  if (!cookieObj['_session']) {
    console.log('❌ Missing _session cookie - this is required for authentication');
  } else if (cookieObj['_session'].length < 100) {
    console.log('⚠️  _session cookie seems too short - may be truncated');
  } else {
    console.log('✅ _session cookie present and appears valid');
  }
  
  console.log('\n🔧 Next Steps:');
  console.log('1. If direct test fails: Extract fresh cookies from browser');
  console.log('2. If direct test passes but proxy fails: Check proxy header forwarding');
  console.log('3. Use extract-auth-data.md guide to get fresh authentication data');
  
  console.log('\n🏁 Validation completed!');
}

// Run validation
validateAuth().catch(console.error);
