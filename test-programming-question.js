const fs = require('fs');

// Test the deployed service with a programming question and record full response
async function testProgrammingQuestion() {
  console.log('🧪 Testing Deployed Service with Programming Question...\n');
  
  const SERVICE_URL = 'https://bolt2api-rf6frxmcca-ew.a.run.app';
  
  // Read fresh cookies
  const cookies = fs.readFileSync('./sample-cookies.txt', 'utf8').trim();
  console.log('🍪 Cookie length:', cookies.length);
  
  // Programming question payload
  const programmingQuestion = {
    id: "programming-test-" + Date.now(),
    errorReasoning: null,
    featurePreviews: { diffs: false, reasoning: false },
    framework: "DEFAULT_TO_DEV",
    isFirstPrompt: false,
    messages: [
      { 
        id: "programming-msg", 
        role: "user", 
        content: "Can you help me create a simple JavaScript function that takes an array of numbers and returns the sum of all even numbers? Please include comments explaining the code."
      }
    ],
    metrics: { importFilesLength: 0, fileChangesLength: 0 },
    projectId: "49956303",
    promptMode: "discussion",
    stripeStatus: "not-configured",
    supportIntegrations: true,
    usesInspectedElement: false
  };
  
  console.log('📋 Programming question ready');
  console.log('❓ Question:', programmingQuestion.messages[0].content);
  console.log('');
  
  // Test deployed service
  console.log('🌐 Testing deployed bolt2api service...');
  console.log('🔗 URL:', SERVICE_URL);
  
  try {
    const startTime = Date.now();
    
    const response = await fetch(`${SERVICE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
        'X-Bolt-Client-Revision': 'd65f6d0',
        'X-Bolt-Project-Id': '49956303'
      },
      body: JSON.stringify(programmingQuestion)
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log('📊 Response Status:', response.status);
    console.log('⏱️  Response Time:', responseTime + 'ms');
    console.log('📄 Content-Type:', response.headers.get('content-type'));
    console.log('');
    
    if (response.status === 200) {
      console.log('✅ Request SUCCESSFUL!');
      
      const data = await response.json();
      console.log('📦 Response Structure:');
      console.log('- success:', data.success);
      console.log('- timestamp:', data.timestamp);
      console.log('- data.type:', data.data?.type);
      console.log('');
      
      if (data.success && data.data) {
        console.log('🎯 FULL RESPONSE CONTENT:');
        console.log('='.repeat(80));
        
        if (data.data.type === 'stream' && data.data.content) {
          console.log(data.data.content);
        } else {
          console.log(JSON.stringify(data.data, null, 2));
        }
        
        console.log('='.repeat(80));
        console.log('');
        
        // Save full response to file for verification
        const responseRecord = {
          timestamp: new Date().toISOString(),
          question: programmingQuestion.messages[0].content,
          responseTime: responseTime,
          status: response.status,
          success: data.success,
          fullResponse: data.data,
          serviceUrl: SERVICE_URL
        };
        
        fs.writeFileSync('./full-response-record.json', JSON.stringify(responseRecord, null, 2));
        console.log('💾 Full response saved to: full-response-record.json');
        
        console.log('\n🎉 SUCCESS SUMMARY:');
        console.log('✅ Deployment: Working');
        console.log('✅ Authentication: Successful');
        console.log('✅ Response: Complete');
        console.log('✅ Content: Programming answer received');
        console.log('✅ Format: Properly handled streaming response');
        
      } else {
        console.log('❌ Response structure issue:', data);
      }
      
    } else {
      console.log('❌ Request FAILED');
      const errorText = await response.text();
      console.log('Error:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
  
  console.log('\n🏁 Full response test completed!');
}

// Run test
testProgrammingQuestion().catch(console.error);
