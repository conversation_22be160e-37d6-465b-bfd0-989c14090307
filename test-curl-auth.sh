#!/bin/bash

# Test authentication with cURL using exact browser headers
# Replace [FRESH_COOKIES] with cookies from DevTools

echo "🧪 Testing bolt.new authentication with cURL..."
echo ""

# Test 1: Direct bolt.new with minimal headers
echo "📋 Test 1: Minimal headers (current cookies)"
curl -s -w "Status: %{http_code}\n" \
  'https://bolt.new/api/chat' \
  -X POST \
  -H 'Content-Type: application/json' \
  -H 'Cookie: __hssc=69929231.2.1749947958589; __hssrc=1; __hstc=69929231.e388db2bbd055574895fe623abc00acc.1749901458275.527a32b3-5d84-4634-819f-75fb14841ed3; _session=eyJkIjoiZVR6SUdsdTlaZlh5cGJnblNjeWVsdVpmNTZlU0liRDRDL1k5VDB5cTJKMTNKU3o1bDRrS0RPa29FcHJDekRNSlo3cjBxRFVTelZpcVl0ajQySzZhU3l4Y29HQUtsbXFHVHk0NkRqcW8vb1RSbDl1TWQyYVdyVWY0YytvWjU1Z2Jnb0RJNXRFakIyVTFpOTdyRi9jK2VBdTYrT0dVaXlQUTM2SWVOdkJWdnAyYnh4Zi9zQ3NUSEMzc0w3eWJnbUlneU1hVDF4YXppejQvUjZwRXB1bXpKZ1VZYURUb0cwMGZsWUpRL25CWkF6UDAzYzJMc2dkcVFLL0xITkFoZ05KWEtDREdrbklOdUFidDRVVkpqV2hoMFRoTGtXbVh2dCtJTXVySjVueWozZWM9In0; _fbp=fb.1.1749901457387.683074235251349705; _ga=GA1.1.687330784.1749901458; _ga_SLJ4P1NJFR=GS2.1.1749947955$o4$g1$t1749947974$j41$l0$h0; _gcl_au=1.1.1806974786.1749901457.135721763.1749945803.1749945802; _rdt_uuid=1749901458275.527a32b3-5d84-4634-819f-75fb14841ed3; activeOrganizationId=Mjl4NTE%3D; ajs_anonymous_id=ODdjMzk0MzctM2E0Yi00YjJiLWI0ZjgtODZhMGJiNTU3NWFm; hubspotutk=e388db2bbd055574895fe623abc00acc' \
  --data-raw '{
    "id": "curl-test-1",
    "errorReasoning": null,
    "featurePreviews": {"diffs": false, "reasoning": false},
    "framework": "DEFAULT_TO_DEV",
    "isFirstPrompt": false,
    "messages": [{"id": "msg1", "role": "user", "content": "Test"}],
    "metrics": {"importFilesLength": 0, "fileChangesLength": 0},
    "projectId": "49956303",
    "promptMode": "discussion",
    "stripeStatus": "not-configured",
    "supportIntegrations": true,
    "usesInspectedElement": false
  }'

echo ""
echo ""

# Test 2: With full browser headers
echo "📋 Test 2: Full browser headers (current cookies)"
curl -s -w "Status: %{http_code}\n" \
  'https://bolt.new/api/chat' \
  -X POST \
  -H 'Accept: */*' \
  -H 'Accept-Encoding: gzip, deflate, br, zstd' \
  -H 'Accept-Language: en-US,en;q=0.9' \
  -H 'Content-Type: application/json' \
  -H 'Cookie: __hssc=69929231.2.1749947958589; __hssrc=1; __hstc=69929231.e388db2bbd055574895fe623abc00acc.1749901458275.527a32b3-5d84-4634-819f-75fb14841ed3; _session=eyJkIjoiZVR6SUdsdTlaZlh5cGJnblNjeWVsdVpmNTZlU0liRDRDL1k5VDB5cTJKMTNKU3o1bDRrS0RPa29FcHJDekRNSlo3cjBxRFVTelZpcVl0ajQySzZhU3l4Y29HQUtsbXFHVHk0NkRqcW8vb1RSbDl1TWQyYVdyVWY0YytvWjU1Z2Jnb0RJNXRFakIyVTFpOTdyRi9jK2VBdTYrT0dVaXlQUTM2SWVOdkJWdnAyYnh4Zi9zQ3NUSEMzc0w3eWJnbUlneU1hVDF4YXppejQvUjZwRXB1bXpKZ1VZYURUb0cwMGZsWUpRL25CWkF6UDAzYzJMc2dkcVFLL0xITkFoZ05KWEtDREdrbklOdUFidDRVVkpqV2hoMFRoTGtXbVh2dCtJTXVySjVueWozZWM9In0; _fbp=fb.1.1749901457387.683074235251349705; _ga=GA1.1.687330784.1749901458; _ga_SLJ4P1NJFR=GS2.1.1749947955$o4$g1$t1749947974$j41$l0$h0; _gcl_au=1.1.1806974786.1749901457.135721763.1749945803.1749945802; _rdt_uuid=1749901458275.527a32b3-5d84-4634-819f-75fb14841ed3; activeOrganizationId=Mjl4NTE%3D; ajs_anonymous_id=ODdjMzk0MzctM2E0Yi00YjJiLWI0ZjgtODZhMGJiNTU3NWFm; hubspotutk=e388db2bbd055574895fe623abc00acc' \
  -H 'Origin: https://bolt.new' \
  -H 'Referer: https://bolt.new/' \
  -H 'Sec-Ch-Ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"' \
  -H 'Sec-Ch-Ua-Mobile: ?0' \
  -H 'Sec-Ch-Ua-Platform: "Windows"' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{
    "id": "curl-test-2",
    "errorReasoning": null,
    "featurePreviews": {"diffs": false, "reasoning": false},
    "framework": "DEFAULT_TO_DEV",
    "isFirstPrompt": false,
    "messages": [{"id": "msg1", "role": "user", "content": "Test"}],
    "metrics": {"importFilesLength": 0, "fileChangesLength": 0},
    "projectId": "49956303",
    "promptMode": "discussion",
    "stripeStatus": "not-configured",
    "supportIntegrations": true,
    "usesInspectedElement": false
  }'

echo ""
echo ""

echo "🔧 To test with fresh cookies:"
echo "1. Open bolt.new in browser"
echo "2. Open DevTools → Network"
echo "3. Make a chat request"
echo "4. Right-click /api/chat request → Copy as cURL"
echo "5. Replace the Cookie header in this script"
echo "6. Run this script again"

echo ""
echo "📝 Expected results:"
echo "✅ Status: 200 = Authentication working"
echo "❌ Status: 401 = Cookies expired/invalid"
echo "❌ Status: 403 = Missing CSRF token or other auth header"
